import json
import os
import sys
from datetime import datetime
from aipyapp import runtime

def print_header(title):
    """打印标题"""
    print(f"\n{'='*50}")
    print(f"🎬 {title}")
    print(f"{'='*50}")

def execute_step1():
    """步骤1：获取热播短剧排行榜"""
    print_header("步骤1：获取热播短剧排行榜")
    
    try:
        # 获取之前保存的排行榜数据
        previous_state = runtime.get_block_state("get_hot_drama_ranking")
        if previous_state and previous_state.get('success'):
            print("✅ 发现已获取的排行榜数据")
            dramas = previous_state.get('dramas', [])
            print(f"📊 数据包含 {len(dramas)} 部热播短剧")
            return True
        else:
            print("🔄 需要重新获取排行榜数据")
            print("💡 请先运行 get_hot_drama_ranking 代码块")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {e}", file=sys.stderr)
        return False

def execute_step2():
    """步骤2：获取短剧详细信息"""
    print_header("步骤2：获取短剧详细信息")
    
    try:
        # 获取之前保存的详细信息
        previous_state = runtime.get_block_state("get_drama_details")
        if previous_state and previous_state.get('success'):
            print("✅ 发现已获取的详细信息")
            hot_dramas = previous_state.get('hot_dramas', [])
            upcoming_dramas = previous_state.get('upcoming_dramas', [])
            print(f"📊 热播短剧: {len(hot_dramas)} 部")
            print(f"🚀 即将上映: {len(upcoming_dramas)} 部")
            return True
        else:
            print("🔄 需要重新获取详细信息")
            print("💡 请先运行 get_drama_details 代码块")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {e}", file=sys.stderr)
        return False

def execute_step3():
    """步骤3：生成爆文HTML代码"""
    print_header("步骤3：生成爆文HTML代码")
    
    try:
        # 获取之前生成的HTML
        previous_state = runtime.get_block_state("generate_viral_article")
        if previous_state and previous_state.get('success'):
            print("✅ 发现已生成的HTML代码")
            title = previous_state.get('article_title', '未知标题')
            html_file = previous_state.get('html_file', '未知文件')
            print(f"📝 文章标题: {title}")
            print(f"📄 HTML文件: {html_file}")
            return True
        else:
            print("🔄 需要重新生成HTML代码")
            print("💡 请先运行 generate_viral_article 代码块")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {e}", file=sys.stderr)
        return False

def execute_step4():
    """步骤4：生成公众号专用HTML"""
    print_header("步骤4：生成公众号专用HTML")
    
    try:
        # 获取之前生成的公众号HTML
        previous_state = runtime.get_block_state("wechat_html_generator")
        if previous_state and previous_state.get('success'):
            print("✅ 发现已生成的公众号专用HTML")
            title = previous_state.get('wechat_title', '未知标题')
            html_file = previous_state.get('wechat_file', '未知文件')
            print(f"📝 文章标题: {title}")
            print(f"📄 HTML文件: {html_file}")
            return True
        else:
            print("🔄 需要重新生成公众号专用HTML")
            print("💡 请先运行 wechat_html_generator 代码块")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {e}", file=sys.stderr)
        return False

def show_status():
    """显示当前状态"""
    print_header("工具状态检查")
    
    steps = [
        ("get_hot_drama_ranking", "📊 获取热播短剧排行榜"),
        ("get_drama_details", "🎬 获取短剧详细信息"),
        ("generate_viral_article", "📝 生成爆文HTML代码"),
        ("wechat_html_generator", "📱 生成公众号专用HTML")
    ]
    
    print("🔍 检查各步骤执行状态：")
    all_completed = True
    
    for step_name, step_desc in steps:
        state = runtime.get_block_state(step_name)
        if state and state.get('success'):
            print(f"   ✅ {step_desc} - 已完成")
        else:
            print(f"   ❌ {step_desc} - 未完成")
            all_completed = False
    
    if all_completed:
        print(f"\n🎉 恭喜！所有步骤已完成！")
        print(f"🚀 您可以直接使用生成的HTML代码了！")
    else:
        print(f"\n⚠️  还有步骤未完成，请按顺序执行：")
        print(f"   1️⃣ get_hot_drama_ranking")
        print(f"   2️⃣ get_drama_details")
        print(f"   3️⃣ generate_viral_article")
        print(f"   4️⃣ wechat_html_generator")

def show_usage_guide():
    """显示使用指南"""
    print_header("短剧公众号工具使用指南")
    
    print(f"📋 工具概述：")
    print(f"   这是一个专为短剧公众号博主设计的自动化工具")
    print(f"   每天自动收集热播短剧信息并生成爆文HTML代码")
    
    print(f"\n🔧 工具组成：")
    print(f"   1️⃣ get_hot_drama_ranking - 获取热播短剧排行榜")
    print(f"   2️⃣ get_drama_details - 获取短剧详细信息和海报")
    print(f"   3️⃣ generate_viral_article - 生成爆文HTML代码")
    print(f"   4️⃣ wechat_html_generator - 生成公众号专用HTML")
    print(f"   5️⃣ simple_drama_scheduler - 工具调度和状态检查")
    
    print(f"\n📊 数据来源：")
    print(f"   📱 抖音平台 - 热播短剧排行榜")
    print(f"   📱 快手平台 - 短剧热度数据")
    print(f"   🎬 各平台预告 - 即将上映短剧信息")
    
    print(f"\n🚀 使用步骤：")
    print(f"   第一步：运行 get_hot_drama_ranking")
    print(f"   第二步：运行 get_drama_details")
    print(f"   第三步：运行 generate_viral_article")
    print(f"   第四步：运行 wechat_html_generator")
    print(f"   第五步：复制HTML代码到公众号后台发布")
    
    print(f"\n💡 公众号发布方法：")
    print(f"   1. 打开生成的HTML文件")
    print(f"   2. 复制全部HTML代码")
    print(f"   3. 登录公众号后台")
    print(f"   4. 新建图文消息")
    print(f"   5. 点击编辑器的 HTML 按钮")
    print(f"   6. 粘贴HTML代码")
    print(f"   7. 预览效果并发布")
    
    print(f"\n⚠️  重要提醒：")
    print(f"   • 当前使用模拟数据进行演示")
    print(f"   • 实际使用需要申请各平台API权限")
    print(f"   • 确保数据获取符合平台规定")
    print(f"   • 每天运行一次获取最新数据")

def main():
    """主函数"""
    print(f"🎬 短剧公众号爆文生成工具 - 简化调度器")
    print(f"💼 您的专属AI助手: AiPy")
    print(f"📅 今日日期: {datetime.now().strftime('%Y年%m月%d日')}")
    
    while True:
        print(f"\n{'='*50}")
        print(f"🔧 请选择操作：")
        print(f"   1️⃣ 检查步骤1状态 (获取排行榜)")
        print(f"   2️⃣ 检查步骤2状态 (获取详细信息)")
        print(f"   3️⃣ 检查步骤3状态 (生成HTML)")
        print(f"   4️⃣ 检查步骤4状态 (公众号HTML)")
        print(f"   5️⃣ 检查所有步骤状态")
        print(f"   6️⃣ 查看完整使用指南")
        print(f"   0️⃣ 退出程序")
        
        try:
            choice = input(f"\n🔧 请输入选项编号 (0-6): ").strip()
            
            if choice == '0':
                print(f"\n👋 感谢使用！再见老板！")
                break
            elif choice == '1':
                execute_step1()
            elif choice == '2':
                execute_step2()
            elif choice == '3':
                execute_step3()
            elif choice == '4':
                execute_step4()
            elif choice == '5':
                show_status()
            elif choice == '6':
                show_usage_guide()
            else:
                print(f"❌ 无效选项，请重新输入")
            
            # 暂停等待用户继续
            if choice != '0':
                input(f"\n⏸️  按回车键继续...")
                
        except KeyboardInterrupt:
            print(f"\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 程序出错: {e}", file=sys.stderr)

if __name__ == "__main__":
    main()