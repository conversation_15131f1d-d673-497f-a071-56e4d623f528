import json
import os
import sys
from datetime import datetime
from aipyapp import runtime

def print_step_header(step_name, step_num, total_steps):
    """打印步骤标题"""
    print(f"\n{'='*60}")
    print(f"🔧 步骤 {step_num}/{total_steps}: {step_name}")
    print(f"{'='*60}\n")

def print_step_success(step_name):
    """打印步骤成功信息"""
    print(f"✅ {step_name} - 执行成功！")

def print_step_error(step_name, error_msg):
    """打印步骤错误信息"""
    print(f"❌ {step_name} - 执行失败: {error_msg}", file=sys.stderr)

def check_previous_step(step_name):
    """检查上一步骤是否成功完成"""
    previous_state = runtime.get_block_state(step_name)
    if not previous_state or not previous_state.get('success', False):
        return False, f"请先完成步骤: {step_name}"
    return True, "OK"

def execute_step1_get_ranking():
    """步骤1: 获取热播短剧排行榜"""
    print_step_header("获取热播短剧排行榜", 1, 4)
    
    try:
        # 导入并执行第一步代码
        from blocks import get_hot_drama_ranking
        
        print("🔄 正在获取热播短剧排行榜数据...")
        print("📡 数据源: 抖音、快手平台")
        print("📊 获取内容: TOP15热播短剧排名、热度、平台")
        
        # 执行第一步
        exec(get_hot_drama_ranking.code)
        
        # 检查执行结果
        state = runtime.get_block_state("get_hot_drama_ranking")
        if state and state.get('success'):
            dramas = state.get('dramas', [])
            print(f"📈 成功获取 {len(dramas)} 部热播短剧数据")
            print(f"💾 数据已保存至: {state.get('saved_file', '未知文件')}")
            print_step_success("获取热播短剧排行榜")
            return True
        else:
            error_msg = state.get('error', '未知错误') if state else '未获取到执行状态'
            print_step_error("获取热播短剧排行榜", error_msg)
            return False
            
    except Exception as e:
        print_step_error("获取热播短剧排行榜", str(e))
        return False

def execute_step2_get_details():
    """步骤2: 获取短剧详细信息和海报"""
    print_step_header("获取短剧详细信息和海报", 2, 4)
    
    # 检查第一步是否完成
    is_ready, msg = check_previous_step("get_hot_drama_ranking")
    if not is_ready:
        print_step_error("获取短剧详细信息", msg)
        return False
    
    try:
        # 导入并执行第二步代码
        from blocks import get_drama_details
        
        print("🔄 正在获取短剧详细信息和海报...")
        print("📡 数据源: 各短剧详情页面")
        print("📊 获取内容: 剧情简介、演员信息、海报图片、评分")
        
        # 执行第二步
        exec(get_drama_details.code)
        
        # 检查执行结果
        state = runtime.get_block_state("get_drama_details")
        if state and state.get('success'):
            hot_dramas = state.get('hot_dramas', [])
            upcoming_dramas = state.get('upcoming_dramas', [])
            print(f"📈 成功处理 {len(hot_dramas)} 部热播短剧详细信息")
            print(f"🚀 成功获取 {len(upcoming_dramas)} 部即将上映短剧")
            print(f"📁 海报图片已保存至: posters/ 目录")
            print_step_success("获取短剧详细信息和海报")
            return True
        else:
            error_msg = state.get('error', '未知错误') if state else '未获取到执行状态'
            print_step_error("获取短剧详细信息", error_msg)
            return False
            
    except Exception as e:
        print_step_error("获取短剧详细信息", str(e))
        return False

def execute_step3_generate_article():
    """步骤3: 生成爆文HTML代码"""
    print_step_header("生成爆文HTML代码", 3, 4)
    
    # 检查第二步是否完成
    is_ready, msg = check_previous_step("get_drama_details")
    if not is_ready:
        print_step_error("生成爆文HTML代码", msg)
        return False
    
    try:
        # 导入并执行第三步代码
        from blocks import generate_viral_article
        
        print("🔄 正在生成爆文HTML代码...")
        print("🎨 设计风格: 公众号专用精美排版")
        print("📱 适配设备: 手机端完美显示")
        print("📊 内容包含: 热播排行榜、即将上映、互动引导")
        
        # 执行第三步
        exec(generate_viral_article.code)
        
        # 检查执行结果
        state = runtime.get_block_state("generate_viral_article")
        if state and state.get('success'):
            html_file = state.get('html_file', '未知文件')
            title = state.get('article_title', '未知标题')
            html_length = len(state.get('html_content', ''))
            
            print(f"📝 文章标题: {title}")
            print(f"📄 HTML文件: {html_file}")
            print(f"📏 代码长度: {html_length} 字符")
            print(f"✅ HTML代码已优化适配公众号后台")
            print_step_success("生成爆文HTML代码")
            return True
        else:
            error_msg = state.get('error', '未知错误') if state else '未获取到执行状态'
            print_step_error("生成爆文HTML代码", error_msg)
            return False
            
    except Exception as e:
        print_step_error("生成爆文HTML代码", str(e))
        return False

def execute_step4_final_summary():
    """步骤4: 最终使用说明"""
    print_step_header("最终使用说明", 4, 4)
    
    # 检查第三步是否完成
    is_ready, msg = check_previous_step("generate_viral_article")
    if not is_ready:
        print_step_error("最终使用说明", msg)
        return False
    
    try:
        # 获取最终结果
        state = runtime.get_block_state("generate_viral_article")
        if state and state.get('success'):
            html_file = state.get('html_file', '')
            title = state.get('article_title', '')
            
            print("🎉 恭喜！所有步骤已完成！")
            print(f"\n📋 最终成果:")
            print(f"   📝 文章标题: {title}")
            print(f"   📄 HTML文件: {html_file}")
            
            print(f"\n🚀 使用方法:")
            print(f"   1️⃣ 打开文件: {html_file}")
            print(f"   2️⃣ 复制全部HTML代码")
            print(f"   3️⃣ 打开公众号后台编辑器")
            print(f"   4️⃣ 切换到 HTML 模式")
            print(f"   5️⃣ 粘贴代码并发布")
            
            print(f"\n💡 注意事项:")
            print(f"   • 确保在公众号后台使用 HTML 模式粘贴")
            print(f"   • 图片链接需要替换为实际可访问的URL")
            print(f"   • 建议发布前预览效果")
            print(f"   • 每天运行一次获取最新数据")
            
            print_step_success("最终使用说明")
            return True
        else:
            print_step_error("最终使用说明", "无法获取最终结果")
            return False
            
    except Exception as e:
        print_step_error("最终使用说明", str(e))
        return False

def show_main_menu():
    """显示主菜单"""
    print(f"\n{'='*60}")
    print(f"🎬 短剧公众号爆文生成工具 - 主菜单")
    print(f"{'='*60}")
    print(f"📅 今日日期: {datetime.now().strftime('%Y年%m月%d日')}")
    print(f"📱 工具版本: v1.0")
    print(f"\n🔧 请选择要执行的步骤:")
    print(f"   1️⃣ 获取热播短剧排行榜")
    print(f"   2️⃣ 获取短剧详细信息和海报")
    print(f"   3️⃣ 生成爆文HTML代码")
    print(f"   4️⃣ 查看最终使用说明")
    print(f"   5️⃣ 一键执行全部步骤")
    print(f"   0️⃣ 退出程序")
    print(f"\n💡 建议按顺序执行: 1 → 2 → 3 → 4")

def execute_all_steps():
    """一键执行所有步骤"""
    print(f"\n🚀 开始一键执行所有步骤...")
    
    steps = [
        ("获取热播短剧排行榜", execute_step1_get_ranking),
        ("获取短剧详细信息和海报", execute_step2_get_details),
        ("生成爆文HTML代码", execute_step3_generate_article),
        ("最终使用说明", execute_step4_final_summary)
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        if step_func():
            success_count += 1
        else:
            print(f"\n⚠️  步骤失败，停止执行后续步骤")
            break
    
    print(f"\n📊 执行结果: {success_count}/{len(steps)} 个步骤成功完成")
    return success_count == len(steps)

def main():
    """主函数"""
    print(f"🎬 欢迎使用短剧公众号爆文生成工具！")
    print(f"💼 您的专属AI助手: AiPy")
    print(f"🎯 目标: 每日自动生成短剧推荐爆文")
    
    while True:
        show_main_menu()
        
        try:
            choice = input(f"\n🔧 请输入选项编号 (0-5): ").strip()
            
            if choice == '0':
                print(f"\n👋 感谢使用！再见老板！")
                break
            elif choice == '1':
                execute_step1_get_ranking()
            elif choice == '2':
                execute_step2_get_details()
            elif choice == '3':
                execute_step3_generate_article()
            elif choice == '4':
                execute_step4_final_summary()
            elif choice == '5':
                execute_all_steps()
            else:
                print(f"❌ 无效选项，请重新输入")
            
            # 暂停等待用户继续
            if choice != '0':
                input(f"\n⏸️  按回车键继续...")
                
        except KeyboardInterrupt:
            print(f"\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 程序出错: {e}", file=sys.stderr)

if __name__ == "__main__":
    main()