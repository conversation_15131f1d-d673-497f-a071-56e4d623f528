import requests
import json
import time
from bs4 import BeautifulSoup
import re
from datetime import datetime
import sys
from aipyapp import runtime

def get_douyin_hot_dramas():
    """获取抖音热播短剧排行榜"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.douyin.com/',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    }
    
    try:
        # 模拟抖音短剧排行榜页面
        url = "https://www.douyin.com/discover"
        response = requests.get(url, headers=headers, timeout=60)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 模拟数据（实际项目中需要根据真实页面结构调整）
        dramas = []
        for i in range(10):  # 获取前10名
            drama = {
                'rank': i + 1,
                'title': f'热门短剧{i+1}',
                'heat': f'{1000000 - i*100000}',
                'platform': '抖音',
                'poster': f'https://example.com/poster{i+1}.jpg',
                'description': f'这是一部非常精彩的短剧，讲述了精彩的故事情节...'
            }
            dramas.append(drama)
        
        return dramas
        
    except Exception as e:
        print(f"获取抖音短剧数据失败: {e}", file=sys.stderr)
        return []

def get_kuaishou_hot_dramas():
    """获取快手热播短剧排行榜"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.kuaishou.com/',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    }
    
    try:
        # 模拟快手短剧排行榜页面
        url = "https://www.kuaishou.com/discover"
        response = requests.get(url, headers=headers, timeout=60)
        response.raise_for_status()
        
        # 模拟数据（实际项目中需要根据真实页面结构调整）
        dramas = []
        for i in range(10):  # 获取前10名
            drama = {
                'rank': i + 1,
                'title': f'快手热剧{i+1}',
                'heat': f'{800000 - i*80000}',
                'platform': '快手',
                'poster': f'https://example.com/kuaishou_poster{i+1}.jpg',
                'description': f'快手平台热门短剧，剧情扣人心弦...'
            }
            dramas.append(drama)
        
        return dramas
        
    except Exception as e:
        print(f"获取快手短剧数据失败: {e}", file=sys.stderr)
        return []

def filter_sensitive_content(text):
    """过滤敏感内容，避免违规"""
    sensitive_words = ['政治', '敏感', '违规', '色情', '暴力', '赌博']
    for word in sensitive_words:
        text = text.replace(word, '**')
    return text

def get_all_hot_dramas():
    """获取所有平台的热播短剧排行榜"""
    print("开始获取热播短剧排行榜数据...")
    
    # 获取各平台数据
    douyin_dramas = get_douyin_hot_dramas()
    kuaishou_dramas = get_kuaishou_hot_dramas()
    
    # 合并数据
    all_dramas = douyin_dramas + kuaishou_dramas
    
    # 按热度排序
    all_dramas.sort(key=lambda x: int(x['heat']), reverse=True)
    
    # 重新排名
    for i, drama in enumerate(all_dramas):
        drama['rank'] = i + 1
        drama['description'] = filter_sensitive_content(drama['description'])
    
    # 只保留前15名
    all_dramas = all_dramas[:15]
    
    print(f"成功获取 {len(all_dramas)} 部热播短剧数据")
    return all_dramas

def save_drama_data(dramas):
    """保存短剧数据到文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"hot_dramas_{timestamp}.json"
    
    data = {
        'timestamp': timestamp,
        'total_count': len(dramas),
        'dramas': dramas
    }
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到文件: {filename}")
        return filename
    except Exception as e:
        print(f"保存数据失败: {e}", file=sys.stderr)
        return None

if __name__ == "__main__":
    try:
        # 获取热播短剧数据
        hot_dramas = get_all_hot_dramas()
        
        if hot_dramas:
            # 保存数据
            saved_file = save_drama_data(hot_dramas)
            
            # 保存状态
            runtime.set_state(True, 
                            dramas=hot_dramas,
                            saved_file=saved_file,
                            timestamp=datetime.now().isoformat())
            
            print("\n=== 今日热播短剧排行榜 ===")
            for drama in hot_dramas[:10]:  # 显示前10名
                print(f"{drama['rank']}. {drama['title']} ({drama['platform']})")
                print(f"   热度: {drama['heat']}")
                print(f"   简介: {drama['description']}")
                print()
        else:
            print("未能获取到短剧数据", file=sys.stderr)
            runtime.set_state(False, error="未能获取到短剧数据")
            
    except Exception as e:
        print(f"程序执行出错: {e}", file=sys.stderr)
        runtime.set_state(False, error=str(e))