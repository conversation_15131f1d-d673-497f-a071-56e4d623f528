{"timestamp": "20250806_112531", "total_count": 15, "dramas": [{"rank": 1, "title": "热门短剧1", "heat": "1000000", "platform": "抖音", "poster": "https://example.com/poster1.jpg", "description": "这是一部非常精彩的短剧，讲述了精彩的故事情节..."}, {"rank": 2, "title": "热门短剧2", "heat": "900000", "platform": "抖音", "poster": "https://example.com/poster2.jpg", "description": "这是一部非常精彩的短剧，讲述了精彩的故事情节..."}, {"rank": 3, "title": "热门短剧3", "heat": "800000", "platform": "抖音", "poster": "https://example.com/poster3.jpg", "description": "这是一部非常精彩的短剧，讲述了精彩的故事情节..."}, {"rank": 4, "title": "快手热剧1", "heat": "800000", "platform": "快手", "poster": "https://example.com/kuaishou_poster1.jpg", "description": "快手平台热门短剧，剧情扣人心弦..."}, {"rank": 5, "title": "快手热剧2", "heat": "720000", "platform": "快手", "poster": "https://example.com/kuaishou_poster2.jpg", "description": "快手平台热门短剧，剧情扣人心弦..."}, {"rank": 6, "title": "热门短剧4", "heat": "700000", "platform": "抖音", "poster": "https://example.com/poster4.jpg", "description": "这是一部非常精彩的短剧，讲述了精彩的故事情节..."}, {"rank": 7, "title": "快手热剧3", "heat": "640000", "platform": "快手", "poster": "https://example.com/kuaishou_poster3.jpg", "description": "快手平台热门短剧，剧情扣人心弦..."}, {"rank": 8, "title": "热门短剧5", "heat": "600000", "platform": "抖音", "poster": "https://example.com/poster5.jpg", "description": "这是一部非常精彩的短剧，讲述了精彩的故事情节..."}, {"rank": 9, "title": "快手热剧4", "heat": "560000", "platform": "快手", "poster": "https://example.com/kuaishou_poster4.jpg", "description": "快手平台热门短剧，剧情扣人心弦..."}, {"rank": 10, "title": "热门短剧6", "heat": "500000", "platform": "抖音", "poster": "https://example.com/poster6.jpg", "description": "这是一部非常精彩的短剧，讲述了精彩的故事情节..."}, {"rank": 11, "title": "快手热剧5", "heat": "480000", "platform": "快手", "poster": "https://example.com/kuaishou_poster5.jpg", "description": "快手平台热门短剧，剧情扣人心弦..."}, {"rank": 12, "title": "热门短剧7", "heat": "400000", "platform": "抖音", "poster": "https://example.com/poster7.jpg", "description": "这是一部非常精彩的短剧，讲述了精彩的故事情节..."}, {"rank": 13, "title": "快手热剧6", "heat": "400000", "platform": "快手", "poster": "https://example.com/kuaishou_poster6.jpg", "description": "快手平台热门短剧，剧情扣人心弦..."}, {"rank": 14, "title": "快手热剧7", "heat": "320000", "platform": "快手", "poster": "https://example.com/kuaishou_poster7.jpg", "description": "快手平台热门短剧，剧情扣人心弦..."}, {"rank": 15, "title": "热门短剧8", "heat": "300000", "platform": "抖音", "poster": "https://example.com/poster8.jpg", "description": "这是一部非常精彩的短剧，讲述了精彩的故事情节..."}]}