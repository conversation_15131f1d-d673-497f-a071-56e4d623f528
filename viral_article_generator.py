import json
import os
from datetime import datetime
import sys
from aipyapp import runtime

def generate_viral_title():
    """生成吸引人的爆文标题"""
    title_templates = [
        "🔥今日必看！{date}热播短剧TOP10，第{top_rank}名太上头了！",
        "💥炸裂推荐！{date}最火短剧排行榜，不看亏大了！",
        "🎬熬夜也要追！{date}短剧热榜，第{top_rank}名绝了！",
        "✨最新出炉！{date}短剧排行榜，第{top_rank}名太精彩！",
        "🌟不看后悔！{date}热播短剧TOP10，第{top_rank}名必追！"
    ]
    
    import random
    template = random.choice(title_templates)
    date_str = datetime.now().strftime("%m月%d日")
    top_rank = random.choice([1, 2, 3])
    
    return template.format(date=date_str, top_rank=top_rank)

def generate_introduction():
    """生成引人入胜的开场白"""
    intros = [
        "姐妹们！今天的短剧热榜来啦！又是一个熬夜追剧的好日子，赶紧收藏起来慢慢看~",
        "宝子们！最新短剧排行榜新鲜出炉！今天这几部真的太上头了，不看绝对后悔！",
        "小伙伴们！今天的短剧推荐来咯！每一部都是精品，赶紧码住！",
        "亲爱的剧迷们！今天的短剧热榜更新啦！这几部真的不容错过！",
        "各位追剧达人！今天的短剧推荐来啦！质量超高，必看系列！"
    ]
    
    import random
    return random.choice(intros)

def generate_drama_review(drama):
    """生成单部短剧的精彩评论"""
    review_templates = [
        "🎬 {title} - 这部剧真的太精彩了！{highlights} 剧情紧凑，演员演技在线，每一集都让人欲罢不能！评分{rating}，强烈推荐！",
        "💫 {title} - 绝对的神作！{highlights} 制作精良，故事情节扣人心弦，看得我熬夜追完！评分{rating}，必看！",
        "🌟 {title} - 太上头了！{highlights} 节奏明快，情感真挚，每一集都有惊喜！评分{rating}，推荐指数五星！",
        "✨ {title} - 精彩绝伦！{highlights} 演员演技精湛，制作水准很高，剧情发展出人意料！评分{rating}，不容错过！",
        "🔥 {title} - 炸裂推荐！{highlights} 剧情跌宕起伏，人物塑造立体，看得我全程投入！评分{rating}，必追系列！"
    ]
    
    import random
    template = random.choice(review_templates)
    highlights = random.choice(drama['highlights'])
    
    return template.format(
        title=drama['title'],
        highlights=highlights,
        rating=drama['audience_rating']
    )

def generate_upcoming_section(upcoming_dramas):
    """生成即将上映短剧的推荐内容"""
    content = "🚀【即将上映精彩预告】🚀\n\n"
    
    for i, drama in enumerate(upcoming_dramas, 1):
        content += f"{i}. 🎬 {drama['title']} ({drama['platform']})\n"
        content += f"   📅 上映时间：{drama['release_date']}\n"
        content += f"   🔥 预计热度：{drama['expected_heat']}\n"
        content += f"   🎭 类型：{drama['genre']}\n"
        content += f"   💫 亮点：{'、'.join(drama['highlights'])}\n"
        content += f"   📝 简介：{drama['description']}\n\n"
    
    content += "💡 小贴士：记得设置提醒，第一时间追更哦！\n"
    return content

def generate_call_to_action():
    """生成互动号召语"""
    ctas = [
        "💬 姐妹们，你们最期待哪一部？快来评论区告诉我！别忘了点赞收藏，明天继续为大家推荐精彩短剧！",
        "👉 宝子们，今天的推荐怎么样？有没有你们正在追的？欢迎在评论区分享你们的观后感！记得关注我，不错过每日更新！",
        "🌟 小伙伴们，这些短剧你们看过几部？快来评论区聊聊！记得点赞收藏，明天同一时间再见！",
        "✨ 亲爱的剧迷们，今天的推荐还满意吗？欢迎在评论区留言讨论！别忘了关注我，获取最新短剧资讯！",
        "🔥 各位追剧达人，今天的推荐够精彩吗？快来评论区互动吧！记得点赞收藏，明天继续为大家带来优质内容！"
    ]
    
    import random
    return random.choice(ctas)

def generate_hashtags():
    """生成热门话题标签"""
    return [
        "#短剧推荐", "#热播短剧", "#今日热榜", "#追剧指南", 
        "#短剧排行榜", "#必看短剧", "#精彩剧情", "#追剧日常",
        "#短剧分享", "#剧情推荐", "#追剧女孩", "#短剧种草"
    ]

def generate_article_content():
    """生成完整的公众号文章内容"""
    # 获取短剧数据
    previous_state = runtime.get_block_state("get_drama_details")
    if not previous_state or 'hot_dramas' not in previous_state:
        print("错误：未找到短剧详细数据", file=sys.stderr)
        return None
    
    hot_dramas = previous_state['hot_dramas']
    upcoming_dramas = previous_state['upcoming_dramas']
    
    # 生成文章内容
    title = generate_viral_title()
    introduction = generate_introduction()
    
    content = f"{title}\n\n"
    content += f"{introduction}\n\n"
    
    # 热播短剧TOP10
    content += "🔥【今日热播短剧TOP10】🔥\n\n"
    
    for i, drama in enumerate(hot_dramas[:10], 1):
        content += f"📊 第{i}名：{drama['title']} ({drama['platform']})\n"
        content += f"   🔥 热度：{drama['heat']}\n"
        content += f"   ⭐ 评分：{drama['audience_rating']}\n"
        content += f"   🎭 类型：{drama['genre']}\n"
        content += f"   📺 集数：{drama['episodes']}\n"
        content += f"   💫 精彩评论：{generate_drama_review(drama)}\n\n"
    
    # 即将上映短剧
    content += generate_upcoming_section(upcoming_dramas)
    
    # 互动号召
    content += generate_call_to_action() + "\n\n"
    
    # 话题标签
    hashtags = generate_hashtags()
    content += "🏷️ 今日话题：\n" + " ".join(hashtags[:8])
    
    return content, title

def generate_html_content():
    """生成公众号HTML代码"""
    # 获取文章内容
    article_content, title = generate_article_content()
    if not article_content:
        return None
    
    # 生成HTML
    html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }}
        .article-container {{
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }}
        .article-title {{
            font-size: 24px;
            font-weight: bold;
            color: #1a1a1a;
            text-align: center;
            margin-bottom: 20px;
            line-height: 1.3;
        }}
        .article-content {{
            font-size: 16px;
            line-height: 1.8;
            color: #333;
        }}
        .section-title {{
            font-size: 18px;
            font-weight: bold;
            color: #ff4757;
            margin: 25px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #ff4757;
        }}
        .drama-item {{
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #ff4757;
        }}
        .drama-rank {{
            font-size: 14px;
            font-weight: bold;
            color: #ff4757;
            margin-bottom: 5px;
        }}
        .drama-title {{
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }}
        .drama-info {{
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }}
        .drama-review {{
            font-size: 14px;
            color: #555;
            margin-top: 8px;
            padding: 8px;
            background: #fff;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }}
        .upcoming-section {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }}
        .upcoming-title {{
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }}
        .upcoming-item {{
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
        }}
        .call-to-action {{
            background: #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
            color: #2d3436;
        }}
        .hashtags {{
            background: #dfe6e9;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }}
        .hashtags-title {{
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }}
        .emoji {{
            font-size: 16px;
            margin-right: 5px;
        }}
    </style>
</head>
<body>
    <div class="article-container">
        <h1 class="article-title">{title}</h1>
        <div class="article-content">
"""
    
    # 将文本内容转换为HTML格式
    lines = article_content.split('\n')
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        if line.startswith('🔥【今日热播短剧TOP10】'):
            html += f'            <div class="section-title">{line}</div>\n'
        elif line.startswith('🚀【即将上映精彩预告】'):
            html += f'            <div class="upcoming-section">\n'
            html += f'                <div class="upcoming-title">{line}</div>\n'
        elif line.startswith('💡 小贴士：'):
            html += f'                <div style="text-align: center; margin-top: 15px; font-style: italic;">{line}</div>\n'
            html += f'            </div>\n'
        elif line.startswith('📊 第'):
            # 短剧条目
            html += f'            <div class="drama-item">\n'
            html += f'                <div class="drama-rank">{line}</div>\n'
        elif line.startswith('🔥 热度：') or line.startswith('⭐ 评分：') or line.startswith('🎭 类型：') or line.startswith('📺 集数：'):
            html += f'                <div class="drama-info">{line}</div>\n'
        elif line.startswith('💫 精彩评论：'):
            html += f'                <div class="drama-review">{line.replace("💫 精彩评论：", "")}</div>\n'
            html += f'            </div>\n'
        elif line.startswith('1. 🎬 ') or line.startswith('2. 🎬 ') or line.startswith('3. 🎬 '):
            html += f'                <div class="upcoming-item">{line}</div>\n'
        elif line.startswith('   📅 ') or line.startswith('   🔥 ') or line.startswith('   🎭 ') or line.startswith('   💫 ') or line.startswith('   📝 '):
            html += f'                <div style="font-size: 13px; margin: 2px 0;">{line}</div>\n'
        elif line.startswith('💬 ') or line.startswith('👉 ') or line.startswith('🌟 ') or line.startswith('✨ ') or line.startswith('🔥 '):
            html += f'            <div class="call-to-action">{line}</div>\n'
        elif line.startswith('🏷️ 今日话题：'):
            html += f'            <div class="hashtags">\n'
            html += f'                <div class="hashtags-title">{line}</div>\n'
        elif line.startswith('#短剧推荐') or line.startswith('#热播短剧'):
            html += f'                <div style="margin: 5px 0;">{line}</div>\n'
            html += f'            </div>\n'
        else:
            # 普通段落
            if line:
                html += f'            <p style="margin: 10px 0;">{line}</p>\n'
    
    html += """        </div>
    </div>
</body>
</html>"""
    
    return html, title

def save_html_content(html_content, title):
    """保存HTML内容到文件"""
    # 创建安全的文件名
    safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).replace(' ', '_')
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"viral_article_{timestamp}_{safe_title[:30]}.html"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"HTML文章已保存到: {filename}")
        return filename
    except Exception as e:
        print(f"保存HTML文件失败: {e}", file=sys.stderr)
        return None

if __name__ == "__main__":
    try:
        print("开始生成爆文内容和HTML代码...")
        
        # 生成HTML内容
        html_content, title = generate_html_content()
        
        if html_content:
            # 保存HTML文件
            html_file = save_html_content(html_content, title)
            
            # 保存状态
            runtime.set_state(True, 
                            html_content=html_content,
                            article_title=title,
                            html_file=html_file,
                            timestamp=datetime.now().isoformat())
            
            print(f"\n=== 爆文生成完成 ===")
            print(f"文章标题: {title}")
            print(f"HTML文件: {html_file}")
            print(f"文章长度: {len(html_content)} 字符")
            
            # 显示文章预览
            print(f"\n=== 文章预览 ===")
            print(f"标题: {title}")
            print(f"HTML代码长度: {len(html_content)} 字符")
            print(f"可直接复制到公众号后台使用！")
            
        else:
            print("生成HTML内容失败", file=sys.stderr)
            runtime.set_state(False, error="生成HTML内容失败")
            
    except Exception as e:
        print(f"程序执行出错: {e}", file=sys.stderr)
        runtime.set_state(False, error=str(e))