import json
import re
import os
from datetime import datetime

def load_drama_data():
    """加载短剧数据文件"""
    try:
        # 尝试加载详细信息文件
        detail_files = [f for f in os.listdir('.') if f.startswith('drama_details_') and f.endswith('.json')]
        if detail_files:
            detail_file = detail_files[0]  # 使用最新的文件
            with open(detail_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        # 如果没有详细信息文件，尝试加载排行榜文件
        ranking_files = [f for f in os.listdir('.') if f.startswith('hot_dramas_') and f.endswith('.json')]
        if ranking_files:
            ranking_file = ranking_files[0]
            with open(ranking_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return {
                    'hot_dramas': data.get('dramas', []),
                    'upcoming_dramas': []
                }
        
        print("❌ 未找到短剧数据文件", file=sys.stderr)
        return None
        
    except Exception as e:
        print(f"❌ 加载数据文件失败: {e}", file=sys.stderr)
        return None

def generate_wechat_compatible_html():
    """生成公众号后台兼容的HTML代码"""
    
    # 加载短剧数据
    drama_data = load_drama_data()
    if not drama_data:
        return None, None
    
    hot_dramas = drama_data.get('hot_dramas', [])
    upcoming_dramas = drama_data.get('upcoming_dramas', [])
    
    # 生成吸引人的标题
    title = "🔥今日必看！热播短剧TOP10，第1名太上头了！"
    
    # 生成公众号专用HTML（简化版，兼容公众号后台）
    html_content = f"""<section style="max-width: 100%; margin: 0 auto; padding: 0; box-sizing: border-box;">
    <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="font-size: 20px; font-weight: bold; color: #333; margin: 0; padding: 10px 0; line-height: 1.4;">
            {title}
        </h1>
    </div>
    
    <div style="font-size: 16px; line-height: 1.8; color: #333; margin-bottom: 20px;">
        <p style="margin: 0 0 15px 0;">姐妹们！今天的短剧热榜来啦！又是一个熬夜追剧的好日子，赶紧收藏起来慢慢看~</p>
    </div>
    
    <!-- 热播短剧TOP10 -->
    <div style="margin: 30px 0;">
        <div style="font-size: 18px; font-weight: bold; color: #ff4757; margin-bottom: 20px; text-align: center; padding-bottom: 10px; border-bottom: 2px solid #ff4757;">
            🔥【今日热播短剧TOP10】🔥
        </div>
"""
    
    # 添加TOP10短剧
    for i, drama in enumerate(hot_dramas[:10], 1):
        html_content += f"""
        <div style="background: #f8f9fa; border-radius: 8px; padding: 15px; margin-bottom: 15px; border-left: 4px solid #ff4757;">
            <div style="font-size: 14px; font-weight: bold; color: #ff4757; margin-bottom: 5px;">
                📊 第{i}名：{drama['title']} ({drama['platform']})
            </div>
            <div style="font-size: 14px; color: #666; margin-bottom: 3px;">
                🔥 热度：{drama['heat']}
            </div>
            <div style="font-size: 14px; color: #666; margin-bottom: 3px;">
                ⭐ 评分：{drama['audience_rating']}
            </div>
            <div style="font-size: 14px; color: #666; margin-bottom: 3px;">
                🎭 类型：{drama['genre']}
            </div>
            <div style="font-size: 14px; color: #666; margin-bottom: 8px;">
                📺 集数：{drama['episodes']}
            </div>
            <div style="font-size: 14px; color: #555; margin-top: 8px; padding: 8px; background: #fff; border-radius: 6px; border: 1px solid #e9ecef;">
                💫 精彩评论：这部剧真的太精彩了！剧情紧凑，演员演技在线，每一集都让人欲罢不能！评分{drama['audience_rating']}，强烈推荐！
            </div>
        </div>
"""
    
    # 添加即将上映短剧
    if upcoming_dramas:
        html_content += """
    </div>
    
    <!-- 即将上映短剧 -->
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; padding: 20px; margin: 30px 0;">
        <div style="font-size: 18px; font-weight: bold; margin-bottom: 15px; text-align: center;">
            🚀【即将上映精彩预告】🚀
        </div>
"""
        
        for i, drama in enumerate(upcoming_dramas[:3], 1):
            html_content += f"""
        <div style="background: rgba(255,255,255,0.1); border-radius: 8px; padding: 12px; margin-bottom: 10px;">
            <div style="font-size: 16px; font-weight: bold; margin-bottom: 5px;">
                {i}. 🎬 {drama['title']} ({drama['platform']})
            </div>
            <div style="font-size: 13px; margin: 2px 0;">
                📅 上映时间：{drama['release_date']}
            </div>
            <div style="font-size: 13px; margin: 2px 0;">
                🔥 预计热度：{drama['expected_heat']}
            </div>
            <div style="font-size: 13px; margin: 2px 0;">
                🎭 类型：{drama['genre']}
            </div>
            <div style="font-size: 13px; margin: 2px 0;">
                💫 亮点：{'、'.join(drama['highlights'])}
            </div>
            <div style="font-size: 13px; margin: 2px 0;">
                📝 简介：{drama['description']}
            </div>
        </div>
"""
        
        html_content += """
        <div style="text-align: center; margin-top: 15px; font-style: italic;">
            💡 小贴士：记得设置提醒，第一时间追更哦！
        </div>
    </div>
"""
    
    # 添加结尾部分
    html_content += """
    
    <!-- 互动号召 -->
    <div style="background: #ffeaa7; border-radius: 8px; padding: 15px; margin: 30px 0; text-align: center;">
        <div style="font-weight: bold; color: #2d3436;">
            💬 姐妹们，你们最期待哪一部？快来评论区告诉我！别忘了点赞收藏，明天继续为大家推荐精彩短剧！
        </div>
    </div>
    
    <!-- 话题标签 -->
    <div style="background: #dfe6e9; border-radius: 8px; padding: 15px; margin-top: 20px;">
        <div style="font-weight: bold; margin-bottom: 8px; color: #2c3e50;">
            🏷️ 今日话题：
        </div>
        <div style="margin: 5px 0;">
            #短剧推荐 #热播短剧 #今日热榜 #追剧指南 #短剧排行榜 #必看短剧 #精彩剧情 #追剧日常
        </div>
    </div>
    
</section>"""
    
    return html_content, title

def clean_html_for_wechat(html_content):
    """清理HTML代码以适配公众号后台"""
    # 移除可能不兼容的标签和属性
    cleaned_html = re.sub(r'<!DOCTYPE[^>]*>', '', html_content)
    cleaned_html = re.sub(r'<html[^>]*>', '', cleaned_html)
    cleaned_html = re.sub(r'</html>', '', cleaned_html)
    cleaned_html = re.sub(r'<head[^>]*>.*?</head>', '', cleaned_html, flags=re.DOTALL)
    cleaned_html = re.sub(r'<body[^>]*>', '', cleaned_html)
    cleaned_html = re.sub(r'</body>', '', cleaned_html)
    
    # 确保所有样式都是内联的
    cleaned_html = cleaned_html.replace('style="', 'style="')
    
    # 移除可能的问题字符
    cleaned_html = cleaned_html.replace('&nbsp;', ' ')
    
    return cleaned_html.strip()

def save_wechat_html(html_content, title):
    """保存公众号专用HTML"""
    # 清理HTML
    cleaned_html = clean_html_for_wechat(html_content)
    
    # 生成文件名
    safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).replace(' ', '_')
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"wechat_article_{timestamp}_{safe_title[:30]}.html"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(cleaned_html)
        print(f"公众号专用HTML已保存到: {filename}")
        return filename
    except Exception as e:
        print(f"保存HTML文件失败: {e}", file=sys.stderr)
        return None

def check_data_files():
    """检查数据文件是否存在"""
    print("🔍 检查数据文件...")
    
    # 检查详细信息文件
    detail_files = [f for f in os.listdir('.') if f.startswith('drama_details_') and f.endswith('.json')]
    if detail_files:
        print(f"✅ 找到详细信息文件: {detail_files[0]}")
        return True
    
    # 检查排行榜文件
    ranking_files = [f for f in os.listdir('.') if f.startswith('hot_dramas_') and f.endswith('.json')]
    if ranking_files:
        print(f"✅ 找到排行榜文件: {ranking_files[0]}")
        return True
    
    print("❌ 未找到任何数据文件")
    print("💡 请确保以下文件存在:")
    print("   - drama_details_*.json (详细信息)")
    print("   - 或 hot_dramas_*.json (排行榜)")
    return False

def main():
    """主函数"""
    print("🎬 短剧公众号爆文生成工具 - 独立运行版")
    print("💼 您的专属AI助手: AiPy")
    print(f"📅 今日日期: {datetime.now().strftime('%Y年%m月%d日')}")
    
    # 检查数据文件
    if not check_data_files():
        return
    
    print("\n🔄 正在生成公众号专用HTML代码...")
    print("📱 适配平台: 微信公众号后台")
    print("🎨 设计风格: 简洁美观，手机端优化")
    print("🔧 兼容性: 移除不兼容标签，确保正常显示")
    
    try:
        # 生成HTML内容
        html_content, title = generate_wechat_compatible_html()
        
        if html_content:
            # 保存HTML文件
            html_file = save_wechat_html(html_content, title)
            
            print(f"\n✅ 公众号专用HTML生成完成！")
            print(f"📝 文章标题: {title}")
            print(f"📄 HTML文件: {html_file}")
            print(f"📏 代码长度: {len(html_content)} 字符")
            
            print(f"\n📋 公众号使用说明:")
            print(f"   1️⃣ 打开文件: {html_file}")
            print(f"   2️⃣ 复制全部HTML代码")
            print(f"   3️⃣ 打开公众号后台")
            print(f"   4️⃣ 点击编辑器的 HTML 按钮")
            print(f"   5️⃣ 粘贴代码并点击确定")
            print(f"   6️⃣ 预览效果并发布")
            
            print(f"\n💡 重要提示:")
            print(f"   • 此HTML代码已优化适配公众号后台")
            print(f"   • 移除了可能不兼容的标签和样式")
            print(f"   • 使用内联样式确保显示效果")
            print(f"   • 建议发布前预览确认效果")
            
        else:
            print("❌ 生成HTML内容失败", file=sys.stderr)
            
    except Exception as e:
        print(f"❌ 程序执行出错: {e}", file=sys.stderr)

if __name__ == "__main__":
    import sys
    main()