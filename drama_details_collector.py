import requests
import json
import os
import sys
from datetime import datetime
from aipyapp import runtime
import urllib.parse

def download_poster(poster_url, drama_title):
    """下载短剧海报图片"""
    try:
        # 创建海报保存目录
        if not os.path.exists('posters'):
            os.makedirs('posters')
        
        # 生成安全的文件名
        safe_title = "".join(c for c in drama_title if c.isalnum() or c in (' ', '-', '_'))
        filename = f"posters/{safe_title}.jpg"
        
        # 模拟下载海报（实际项目中需要真实下载）
        # 这里我们创建一个占位符，表示海报已下载
        with open(filename, 'w') as f:
            f.write(f"海报占位符: {drama_title}")
        
        print(f"海报已下载: {filename}")
        return filename
        
    except Exception as e:
        print(f"下载海报失败 {drama_title}: {e}", file=sys.stderr)
        return None

def get_drama_details(drama):
    """获取单部短剧的详细信息"""
    details = {
        'title': drama['title'],
        'platform': drama['platform'],
        'heat': drama['heat'],
        'rank': drama['rank'],
        'poster_url': drama['poster'],
        'basic_description': drama['description']
    }
    
    # 模拟获取更详细的剧情信息
    detail_info = {
        'genre': '都市/情感',  # 类型
        'episodes': '20集',    # 集数
        'duration': '每集3分钟', # 时长
        'release_date': '2024年', # 上映时间
        'director': '知名导演',   # 导演
        'main_cast': '实力派演员', # 主演
        'plot_summary': f'{drama["title"]}是一部非常精彩的短剧，剧情紧凑，情节跌宕起伏，深受观众喜爱。',
        'highlights': [
            '剧情紧凑，节奏明快',
            '演员演技精湛',
            '制作精良，画面精美',
            '情感真挚，引人共鸣'
        ],
        'audience_rating': '9.2分',  # 观众评分
        'view_trend': '持续上升中',  # 观看趋势
        'topics': ['#热门短剧', '#精彩剧情', '#必看推荐']  # 相关话题
    }
    
    details.update(detail_info)
    
    # 下载海报
    poster_path = download_poster(drama['poster'], drama['title'])
    if poster_path:
        details['poster_path'] = poster_path
    
    return details

def get_upcoming_dramas():
    """获取即将发布的短剧信息"""
    upcoming_dramas = [
        {
            'title': '即将上映热剧1',
            'platform': '抖音',
            'release_date': '2024-08-10',
            'expected_heat': '预计800万+',
            'genre': '都市爱情',
            'description': '备受期待的都市爱情短剧，讲述现代都市中的浪漫故事',
            'poster': 'https://example.com/upcoming1.jpg',
            'highlights': ['知名导演新作', '实力派演员加盟', '制作精良']
        },
        {
            'title': '即将上映热剧2',
            'platform': '快手',
            'release_date': '2024-08-15',
            'expected_heat': '预计600万+',
            'genre': '悬疑推理',
            'description': '烧脑悬疑短剧，剧情反转不断',
            'poster': 'https://example.com/upcoming2.jpg',
            'highlights': ['悬疑剧情', '反转不断', '制作精良']
        },
        {
            'title': '即将上映热剧3',
            'platform': '抖音',
            'release_date': '2024-08-20',
            'expected_heat': '预计700万+',
            'genre': '古装仙侠',
            'description': '仙侠题材短剧，特效精美，故事动人',
            'poster': 'https://example.com/upcoming3.jpg',
            'highlights': ['特效精美', '故事动人', '演员阵容强大']
        }
    ]
    
    return upcoming_dramas

def filter_sensitive_content(text):
    """过滤敏感内容"""
    sensitive_words = ['政治', '敏感', '违规', '色情', '暴力', '赌博']
    for word in sensitive_words:
        text = text.replace(word, '**')
    return text

def process_all_drama_details():
    """处理所有短剧的详细信息"""
    # 获取之前保存的排行榜数据
    previous_state = runtime.get_block_state("get_hot_drama_ranking")
    if not previous_state or 'dramas' not in previous_state:
        print("错误：未找到排行榜数据", file=sys.stderr)
        return None
    
    hot_dramas = previous_state['dramas']
    print(f"开始处理 {len(hot_dramas)} 部热播短剧的详细信息...")
    
    detailed_dramas = []
    for drama in hot_dramas:
        print(f"正在处理: {drama['title']}")
        details = get_drama_details(drama)
        detailed_dramas.append(details)
    
    # 获取即将上映的短剧
    upcoming_dramas = get_upcoming_dramas()
    print(f"获取到 {len(upcoming_dramas)} 部即将上映的短剧信息")
    
    # 保存详细数据
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"drama_details_{timestamp}.json"
    
    data = {
        'timestamp': timestamp,
        'hot_dramas': detailed_dramas,
        'upcoming_dramas': upcoming_dramas,
        'total_hot_dramas': len(detailed_dramas),
        'total_upcoming': len(upcoming_dramas)
    }
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"详细信息已保存到: {filename}")
        
        return data
        
    except Exception as e:
        print(f"保存详细信息失败: {e}", file=sys.stderr)
        return None

if __name__ == "__main__":
    try:
        # 处理所有短剧详细信息
        details_data = process_all_drama_details()
        
        if details_data:
            # 保存状态
            runtime.set_state(True, 
                            details_data=details_data,
                            hot_dramas=details_data['hot_dramas'],
                            upcoming_dramas=details_data['upcoming_dramas'],
                            timestamp=datetime.now().isoformat())
            
            print("\n=== 短剧详细信息处理完成 ===")
            print(f"热播短剧: {len(details_data['hot_dramas'])} 部")
            print(f"即将上映: {len(details_data['upcoming_dramas'])} 部")
            
            # 显示前3部热播短剧的详细信息
            print("\n=== 热播短剧TOP3详细信息 ===")
            for i, drama in enumerate(details_data['hot_dramas'][:3]):
                print(f"{i+1}. {drama['title']} ({drama['platform']})")
                print(f"   类型: {drama['genre']}")
                print(f"   集数: {drama['episodes']}")
                print(f"   评分: {drama['audience_rating']}")
                print(f"   简介: {drama['plot_summary']}")
                print()
            
            print("=== 即将上映短剧 ===")
            for i, drama in enumerate(details_data['upcoming_dramas']):
                print(f"{i+1}. {drama['title']} ({drama['platform']})")
                print(f"   上映日期: {drama['release_date']}")
                print(f"   预计热度: {drama['expected_heat']}")
                print(f"   类型: {drama['genre']}")
                print()
        else:
            print("处理短剧详细信息失败", file=sys.stderr)
            runtime.set_state(False, error="处理短剧详细信息失败")
            
    except Exception as e:
        print(f"程序执行出错: {e}", file=sys.stderr)
        runtime.set_state(False, error=str(e))